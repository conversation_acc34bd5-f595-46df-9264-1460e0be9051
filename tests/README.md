# Тесты для ZenohWidget

Этот каталог содержит тесты для проверки функциональности ZenohWidget, особенно системы извлечения полей из вложенных структур.

## Структура тестов

### test_field_extraction.py

Основные тесты для проверки синтаксиса путей к полям:

- **Простые поля**: `"x"` → `float`
- **Одномерные массивы**: `"points[].x"` → `List[float]`
- **Двумерные массивы**: `"segments[].points[].x"` → `List[List[float]]`
- **Трехмерные массивы**: `"tasks[].segments[].points[].x"` → `List[List[List[float]]]`
- **Массивы объектов**: `"segments[].points[]"` → `List[List[Point]]`
- **Вложенные поля**: `"pose.position.x"` → `float`
- **Обработка ошибок**: пустые массивы, None значения

## Запуск тестов

### Запуск всех тестов:
```bash
cd /path/to/project
python -m pytest tests/
```

### Запуск конкретного теста:
```bash
python -m pytest tests/test_field_extraction.py
```

### Запуск с подробным выводом:
```bash
python -m pytest tests/ -v
```

### Альтернативный запуск через unittest:
```bash
python tests/test_field_extraction.py
```

## Тестовые данные

Тесты используют заглушки (mock objects) для имитации:
- ROS2 сообщений с вложенными структурами
- PyQt6 компонентов (QWidget, QTimer, сигналы)
- Core объекта приложения

Это позволяет запускать тесты без зависимостей от PyQt6 и ROS2.

## Добавление новых тестов

При добавлении новой функциональности в ZenohWidget:

1. Создайте соответствующие тестовые случаи
2. Используйте существующие заглушки или создайте новые
3. Проверьте все граничные случаи (пустые данные, None, ошибки)
4. Убедитесь, что тесты проходят независимо от внешних зависимостей

## Покрытие тестами

Текущие тесты покрывают:
- ✅ Извлечение простых полей
- ✅ Одномерные массивы
- ✅ Двумерные массивы  
- ✅ Трехмерные массивы
- ✅ Массивы объектов
- ✅ Вложенные поля
- ✅ Обработка ошибок
- ✅ Пустые массивы
- ✅ None значения

Планируется добавить:
- [ ] Тесты для сервисов как источников данных
- [ ] Тесты для применения множителей
- [ ] Тесты для трансформаций данных
- [ ] Интеграционные тесты с реальными ROS2 сообщениями
