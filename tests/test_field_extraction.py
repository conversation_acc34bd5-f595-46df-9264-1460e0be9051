#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Тесты для проверки извлечения полей из вложенных структур в ZenohWidget.

Проверяет корректность работы синтаксиса путей к полям:
- Простые поля: "x" → float
- Одномерные массивы: "points[].x" → List[float]  
- Двумерные массивы: "segments[].points[].x" → List[List[float]]
- Трехмерные массивы: "tasks[].segments[].points[].x" → List[List[List[float]]]
- Массивы объектов: "segments[].points[]" → List[List[Point]]
"""

import sys
import os
import unittest
from typing import List, Any

# Добавляем корневую директорию проекта в путь
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Создаем минимальные заглушки для PyQt6
class MockQWidget:
    def __init__(self, *args, **kwargs):
        pass

class MockQTimer:
    def __init__(self, *args, **kwargs):
        self.timeout = MockSignal()
    
    def start(self, *args):
        pass
    
    def stop(self):
        pass
    
    def isActive(self):
        return False

class MockSignal:
    def connect(self, *args):
        pass
    
    def disconnect(self, *args):
        pass
    
    def emit(self, *args):
        pass

# Подменяем PyQt6 модули перед импортом
sys.modules['PyQt6'] = type(sys)('PyQt6')
sys.modules['PyQt6.QtCore'] = type(sys)('QtCore')
sys.modules['PyQt6.QtWidgets'] = type(sys)('QtWidgets')

sys.modules['PyQt6.QtCore'].QTimer = MockQTimer
sys.modules['PyQt6.QtCore'].pyqtSignal = lambda *args: MockSignal()
sys.modules['PyQt6.QtWidgets'].QWidget = MockQWidget

# Теперь можем импортировать base_widget
from core.base_widget import ZenohWidget

class MockCore:
    """Заглушка для core объекта"""
    pass

class MockPoint:
    """Заглушка для точки"""
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y
    
    def __eq__(self, other):
        return isinstance(other, MockPoint) and self.x == other.x and self.y == other.y
    
    def __repr__(self):
        return f"Point({self.x}, {self.y})"

class MockSegment:
    """Заглушка для сегмента"""
    def __init__(self, points: List[MockPoint]):
        self.points = points

class MockTask:
    """Заглушка для задачи"""
    def __init__(self, segments: List[MockSegment]):
        self.segments = segments

class MockMessage:
    """Заглушка для сообщения с сегментами"""
    def __init__(self, segments: List[MockSegment]):
        self.path_segments = segments

class MockMissionMessage:
    """Заглушка для сообщения с задачами"""
    def __init__(self, tasks: List[MockTask]):
        self.tasks = tasks

class TestFieldExtraction(unittest.TestCase):
    """Тесты извлечения полей"""
    
    def setUp(self):
        """Подготовка тестовых данных"""
        self.core = MockCore()
        self.widget = ZenohWidget(self.core)
        
        # Создаем тестовые данные для 2D структуры
        self.segments_2d = [
            MockSegment([
                MockPoint(1.0, 10.0),
                MockPoint(2.0, 20.0)
            ]),
            MockSegment([
                MockPoint(3.0, 30.0),
                MockPoint(4.0, 40.0),
                MockPoint(5.0, 50.0)
            ]),
            MockSegment([
                MockPoint(6.0, 60.0)
            ])
        ]
        
        # Создаем тестовые данные для 3D структуры
        self.tasks_3d = [
            MockTask([
                MockSegment([MockPoint(1.0, 10.0), MockPoint(2.0, 20.0)]),
                MockSegment([MockPoint(3.0, 30.0)])
            ]),
            MockTask([
                MockSegment([MockPoint(4.0, 40.0), MockPoint(5.0, 50.0), MockPoint(6.0, 60.0)])
            ])
        ]
    
    def test_simple_field_extraction(self):
        """Тест извлечения простого поля"""
        class MockRobot:
            def __init__(self, x):
                self.x = x
        
        robot = MockRobot(42.0)
        result = self.widget._extract_field_value(robot, "x")
        
        self.assertEqual(result, 42.0)
        self.assertIsInstance(result, float)
    
    def test_1d_array_extraction(self):
        """Тест извлечения одномерного массива"""
        points = [MockPoint(1.0, 10.0), MockPoint(2.0, 20.0), MockPoint(3.0, 30.0)]
        
        class MockPath:
            def __init__(self, points):
                self.points = points
        
        path = MockPath(points)
        result = self.widget._extract_field_value(path, "points[].x")
        
        expected = [1.0, 2.0, 3.0]
        self.assertEqual(result, expected)
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 3)
    
    def test_2d_array_extraction(self):
        """Тест извлечения двумерного массива"""
        message = MockMessage(self.segments_2d)
        
        # Тестируем x-координаты
        result_x = self.widget._extract_field_value(message, "path_segments[].points[].x")
        expected_x = [[1.0, 2.0], [3.0, 4.0, 5.0], [6.0]]
        
        self.assertEqual(result_x, expected_x)
        self.assertIsInstance(result_x, list)
        self.assertEqual(len(result_x), 3)
        self.assertEqual(len(result_x[0]), 2)
        self.assertEqual(len(result_x[1]), 3)
        self.assertEqual(len(result_x[2]), 1)
        
        # Тестируем y-координаты
        result_y = self.widget._extract_field_value(message, "path_segments[].points[].y")
        expected_y = [[10.0, 20.0], [30.0, 40.0, 50.0], [60.0]]
        
        self.assertEqual(result_y, expected_y)
    
    def test_3d_array_extraction(self):
        """Тест извлечения трехмерного массива"""
        mission = MockMissionMessage(self.tasks_3d)
        
        result = self.widget._extract_field_value(mission, "tasks[].segments[].points[].x")
        expected = [[[1.0, 2.0], [3.0]], [[4.0, 5.0, 6.0]]]
        
        self.assertEqual(result, expected)
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)  # 2 задачи
        self.assertEqual(len(result[0]), 2)  # 2 сегмента в первой задаче
        self.assertEqual(len(result[1]), 1)  # 1 сегмент во второй задаче
        self.assertEqual(len(result[0][0]), 2)  # 2 точки в первом сегменте первой задачи
        self.assertEqual(len(result[0][1]), 1)  # 1 точка во втором сегменте первой задачи
        self.assertEqual(len(result[1][0]), 3)  # 3 точки в единственном сегменте второй задачи
    
    def test_object_array_extraction(self):
        """Тест извлечения массивов объектов"""
        message = MockMessage(self.segments_2d)
        
        result = self.widget._extract_field_value(message, "path_segments[].points[]")
        
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 3)  # 3 сегмента
        
        # Проверяем первый сегмент
        self.assertEqual(len(result[0]), 2)
        self.assertIsInstance(result[0][0], MockPoint)
        self.assertEqual(result[0][0].x, 1.0)
        self.assertEqual(result[0][0].y, 10.0)
        
        # Проверяем второй сегмент
        self.assertEqual(len(result[1]), 3)
        self.assertEqual(result[1][2].x, 5.0)
        self.assertEqual(result[1][2].y, 50.0)
    
    def test_nested_field_extraction(self):
        """Тест извлечения вложенных полей"""
        class MockPose:
            def __init__(self, x, y):
                self.position = MockPoint(x, y)
        
        class MockRobot:
            def __init__(self, x, y):
                self.pose = MockPose(x, y)
        
        robot = MockRobot(42.0, 24.0)
        result = self.widget._extract_field_value(robot, "pose.position.x")
        
        self.assertEqual(result, 42.0)
    
    def test_empty_arrays(self):
        """Тест обработки пустых массивов"""
        empty_message = MockMessage([])
        result = self.widget._extract_field_value(empty_message, "path_segments[].points[].x")
        
        self.assertEqual(result, [])
        self.assertIsInstance(result, list)
    
    def test_none_handling(self):
        """Тест обработки None значений"""
        result = self.widget._extract_field_value(None, "some.field")
        self.assertIsNone(result)
        
        class MockWithNone:
            def __init__(self):
                self.field = None
        
        obj = MockWithNone()
        result = self.widget._extract_field_value(obj, "field.subfield")
        self.assertIsNone(result)

if __name__ == '__main__':
    unittest.main()
