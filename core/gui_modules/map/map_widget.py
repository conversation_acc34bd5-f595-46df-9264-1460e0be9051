#!/usr/bin/env python
# -*- coding: utf-8 -*-

import math
import pyqtgraph as pg
import time
from typing import Dict, List, Any, Optional, Tuple

from PyQt6.QtCore import QTimer, Qt, QRectF, QPointF
from PyQt6.QtGui import QColor, <PERSON>Font
from PyQt6.QtWidgets import <PERSON><PERSON>idge<PERSON>, QVBoxLayout, QPushButton, QHBoxLayout, QLabel

from base_widget import ZenohWidget


class MapWidget(ZenohWidget):
    """Map widget for displaying vehicle position, routes, and drilling locations.

    This widget provides a 2D map view of the drilling operation, showing the vehicle,
    planned routes, obstacles, and hole locations. It supports zooming, panning, and
    auto-centering on the vehicle.
    """

    def __init__(self, core: Any, *args, **kwargs) -> None:
        """Initialize the MapWidget.

        Args:
            core: The core application object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
        """
        # Initialize ZenohWidget
        super().__init__(core, *args, **kwargs)
        self.killed = False

        # Initialize attributes that will be set later
        self.viewbox = None
        self.plot = None
        self.grid_lines: List[pg.InfiniteLine] = []
        self.coord_label = None
        self.auto_center_btn = None
        self.reset_view_btn = None
        self.status_label = None
        self.button_proxy = None
        self.status_proxy = None
        self.proxy = None
        self.vehicle_outline = None
        self.left_cat_outline = None
        self.right_cat_outline = None
        self.drill_marker_h = None
        self.drill_marker_v = None
        self.goal_outline = None
        self.full_route = None
        self.planned_route = None
        self.tailing_lines: List[pg.PlotDataItem] = []
        self.arrows_x: List[float] = []
        self.arrows_y: List[float] = []
        self.arrow_plot = None
        self.border_lines = None
        self.obstacles = None
        self.finished_holes = None
        self.active_hole = None
        self.pending_holes = None
        self.hole_labels: List[pg.TextItem] = []
        self.hole_info: List[Dict[str, Any]] = []
        self.update_timer = None
        self.plan_timer = None

        # Create an empty layout - will be populated in _initialize_ui
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # Data storage for current values
        self._current_values = {
            'position_x': 0.0,
            'position_y': 0.0,
            'yaw': 0.0,
            'obstacles_vis': [],
            'safety_berm_map': [],
            'border_vis': []
        }

        # Default vehicle polygon points (from parameter server data)
        # TODO: Get these from parameter server REST API
        self.default_polygon_points = [
            {'x': 6.11, 'y': -1.54},
            {'x': 2.66, 'y': -1.54},
            {'x': 2.06, 'y': -2.13},
            {'x': -5.71, 'y': -2.13},
            {'x': -5.71, 'y': 2.13},
            {'x': 1.42, 'y': 2.13},
            {'x': 1.42, 'y': 3.5},
            {'x': 6.11, 'y': 3.5}
        ]

        self.default_left_cat_points = [
            {'x': 1.7, 'y': -2.2},
            {'x': 1.7, 'y': -1.3},
            {'x': -4.8, 'y': -1.3},
            {'x': -4.8, 'y': -2.2}
        ]

        self.default_right_cat_points = [
            {'x': 1.7, 'y': 1.3},
            {'x': 1.7, 'y': 2.2},
            {'x': -4.8, 'y': 2.2},
            {'x': -4.8, 'y': 1.3}
        ]

        # Drill coordinates (tower to center distance)
        self.tower2center = 2.8

        # Smart route management - кэш для routes и умная логика запросов
        self.cached_routes = None
        self.cached_action_id = None

        # Текущие значения из DriveStatus
        self.current_action_id = None
        self.current_segment_id = None
        self.current_point_id = None

    def prepare(self, width: int = 500, height: int = 500, x: int = 500, y: int = 800, screen_num: int = 1,
                data_sources: Dict = None, **kwargs) -> None:
        """Configure widget position and size - we need to set the parent and position first.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            screen_num: Screen number (0 for sensor screen, 1+ for main screen)
            data_sources: Dictionary of data source configurations for zenoh subscriptions
            vehicle_polygon: List of points defining vehicle shape
            left_caterpillar_polygon: List of points for left caterpillar
            right_caterpillar_polygon: List of points for right caterpillar
            tower_to_center_distance: Distance from vehicle center to drill tower
        """
        # Store vehicle configuration - hardcoded for now, will be fetched from REST API later
        # TODO: Get these parameters from parameter server REST API instead of hardcoding
        self.vehicle_polygon = [
            {"x": 6.11, "y": -1.54}, {"x": 2.66, "y": -1.54}, {"x": 2.06, "y": -2.13},
            {"x": -5.71, "y": -2.13}, {"x": -5.71, "y": 2.13}, {"x": 1.42, "y": 2.13},
            {"x": 1.42, "y": 3.5}, {"x": 6.11, "y": 3.5}
        ]
        self.left_caterpillar_polygon = [
            {"x": 1.7, "y": -2.2}, {"x": 1.7, "y": -1.3}, {"x": -4.8, "y": -1.3}, {"x": -4.8, "y": -2.2}
        ]
        self.right_caterpillar_polygon = [
            {"x": 1.7, "y": 1.3}, {"x": 1.7, "y": 2.2}, {"x": -4.8, "y": 2.2}, {"x": -4.8, "y": 1.3}
        ]
        # Distance from vehicle center to drill tower - hardcoded for now
        # TODO: Get this parameter from parameter server REST API instead of hardcoding
        self.tower_to_center_distance = 2.8
        # Set up default data sources if none provided
        if data_sources is None:
            data_sources = {
                'position_x': {
                    'topic': 'position',
                    'msg_type': 'drill_msgs/msg/Position',
                    'field': 'x'
                },
                'position_y': {
                    'topic': 'position',
                    'msg_type': 'drill_msgs/msg/Position',
                    'field': 'y'
                },
                'yaw': {
                    'topic': 'position',
                    'msg_type': 'drill_msgs/msg/Position',
                    'field': 'yaw'
                },
                'driver_action_id': {
                    'topic': 'driver_status',
                    'msg_type': 'drill_msgs/msg/DriveStatus',
                    'field': 'cur_action_id'
                },
                'driver_segment_id': {
                    'topic': 'driver_status',
                    'msg_type': 'drill_msgs/msg/DriveStatus',
                    'field': 'cur_segment_id'
                },
                'driver_point_id': {
                    'topic': 'driver_status',
                    'msg_type': 'drill_msgs/msg/DriveStatus',
                    'field': 'cur_point_id'
                },
                'obstacles_vis': {
                    'topic': 'obstacles_vis',
                    'msg_type': 'visualization_msgs/msg/MarkerArray',
                    'field': 'markers'
                },
                'safety_berm_map': {
                    'topic': 'safety_berm_map',
                    'msg_type': 'visualization_msgs/msg/MarkerArray',
                    'field': 'markers'
                },
                'border_vis': {
                    'topic': 'border_vis',
                    'msg_type': 'visualization_msgs/msg/MarkerArray',
                    'field': 'markers'
                }
            }

        # Initialize ZenohWidget with data sources
        super().prepare(data_sources=data_sources)

        # Connect to data updates from zenoh widget
        self.data_updated.connect(self._on_data_updated)

        # First set parent and geometry (position)
        if screen_num == 0:
            self.setParent(self.core.sensor_screen)
        else:
            self.setParent(self.core.main_screen)

        # Remove frame/border
        self.setStyleSheet("border: none; background-color: transparent;")

        # Set geometry
        if screen_num == 0:
            self.setGeometry(x, y, width, height)
        else:
            self.setGeometry(self.core.screen_width * (screen_num - 1) + x, y, width, height)

        # Only now initialize the UI components with the proper parent
        self._initialize_ui()

    def _on_data_updated(self, source_name: str, value: Any) -> None:
        """Handle data updates from zenoh subscriptions."""
        # Store all incoming data (not just predefined keys)
        self._current_values[source_name] = value

        # Trigger visualization update for relevant data
        if source_name in ['position_x', 'position_y', 'yaw']:
            # Update current vehicle position if we have both x and y
            x = self._current_values.get('position_x')
            y = self._current_values.get('position_y')
            if x is not None and y is not None:
                self.current_vehicle_position = (x, y)

        # Обработка новых полей DriveStatus для умного управления routes
        elif source_name == "driver_action_id":
            # Используем специальный обработчик для action_id
            self.handle_driver_action_id(value)

        elif source_name == "driver_segment_id":
            self.current_segment_id = value

        elif source_name == "driver_point_id":
            self.current_point_id = value

        # Handle route trajectory data (2D arrays from service)
        elif source_name in ('route_x', 'route_y'):
            self._update_route_visualization()

        # Handle full route trajectory data (flattened arrays from service)
        elif source_name in ('full_route_x', 'full_route_y'):
            self._update_full_route_visualization()

        # Handle obstacles data (2D arrays from topics)
        elif source_name in ('obstacles_x', 'obstacles_y'):
            self._update_obstacles_visualization()

        # Handle safety lines data (2D arrays from topics)
        elif source_name in ('safety_lines_x', 'safety_lines_y'):
            self._update_safety_lines_visualization()

        # Handle border data (2D arrays from topics)
        elif source_name in ('border_x', 'border_y'):
            self._update_border_visualization()

        # Note: Individual update methods will be called by the main update timer

    def on_namespace_changed(self):
        """Переопределяем метод из ZenohWidget для обработки смены машины."""
        # Вызываем родительский метод
        super().on_namespace_changed()

        # Сбрасываем кэш routes при смене машины
        self.cached_routes = None
        self.cached_action_id = None
        self.current_action_id = None
        self.current_segment_id = None
        self.current_point_id = None

        print(f"Namespace changed to '{self.current_namespace}', routes cache cleared")

    def _update_route_visualization(self) -> None:
        """Update route visualization from 2D coordinate arrays with current segment highlighting."""
        try:
            route_x = self._current_values.get('route_x', [])
            route_y = self._current_values.get('route_y', [])

            if not route_x or not route_y or len(route_x) != len(route_y):
                self.planned_route.setData([], [])
                self.current_route_line.setData([], [])
                return

            # route_x and route_y are 2D arrays: List[List[float]]
            # Each sub-list represents one segment
            current_segment_id = self.current_segment_id or 0
            current_point_id = self.current_point_id or 0

            # Draw ALL segments as planned route (gray)
            all_x = []
            all_y = []
            for seg_x, seg_y in zip(route_x, route_y):
                if seg_x and seg_y and len(seg_x) == len(seg_y):
                    # Filter out None values
                    valid_points = [(x, y) for x, y in zip(seg_x, seg_y) if x is not None and y is not None]
                    if valid_points:
                        valid_x, valid_y = zip(*valid_points)
                        all_x.extend(valid_x)
                        all_y.extend(valid_y)
                        # Add None to separate segments visually
                        all_x.append(None)
                        all_y.append(None)

            # Remove last None if exists
            if all_x and all_x[-1] is None:
                all_x = all_x[:-1]
                all_y = all_y[:-1]

            self.planned_route.setData(all_x, all_y)

            # Draw current segment from current point to end in different color
            if (current_segment_id < len(route_x) and
                current_segment_id < len(route_y)):

                segment_x = route_x[current_segment_id]
                segment_y = route_y[current_segment_id]

                if (segment_x and segment_y and
                    len(segment_x) == len(segment_y) and
                    current_point_id < len(segment_x)):

                    # Current segment from current point to end - filter None values
                    current_segment_points = [(x, y) for x, y in zip(segment_x[current_point_id:], segment_y[current_point_id:])
                                            if x is not None and y is not None]

                    if current_segment_points:
                        current_x, current_y = zip(*current_segment_points)
                        self.current_route_line.setData(current_x, current_y)
                        print(f"Updated route: {len(all_x)} total points, current segment {current_segment_id} from point {current_point_id}")
                    else:
                        self.current_route_line.setData([], [])
                else:
                    self.current_route_line.setData([], [])
            else:
                self.current_route_line.setData([], [])

        except Exception as e:
            print(f"Error updating route visualization: {e}")
            self.planned_route.setData([], [])
            self.current_route_line.setData([], [])

    def _update_full_route_visualization(self) -> None:
        """Update full route visualization from flattened coordinate arrays."""
        try:
            full_route_x = self._current_values.get('full_route_x', [])
            full_route_y = self._current_values.get('full_route_y', [])

            if not full_route_x or not full_route_y:
                self.full_route.setData([], [])
                return

            # Flatten 2D arrays to 1D for visualization
            if isinstance(full_route_x[0], list):
                # 2D array - flatten it
                flat_x = [x for segment in full_route_x for x in segment]
                flat_y = [y for segment in full_route_y for y in segment]
            else:
                # Already 1D array
                flat_x = full_route_x
                flat_y = full_route_y

            if len(flat_x) == len(flat_y):
                self.full_route.setData(flat_x, flat_y)
                print(f"Updated full route visualization with {len(flat_x)} points")
            else:
                self.full_route.setData([], [])
                print(f"Full route coordinate mismatch: x={len(flat_x)}, y={len(flat_y)}")

        except Exception as e:
            print(f"Error updating full route visualization: {e}")
            self.full_route.setData([], [])

    def _update_obstacles_visualization(self) -> None:
        """Update obstacles visualization from 2D coordinate arrays."""
        try:
            obstacles_x = self._current_values.get('obstacles_x', [])
            obstacles_y = self._current_values.get('obstacles_y', [])

            if not obstacles_x or not obstacles_y:
                self.obstacles.setData([], [])
                return

            # Flatten 2D arrays (markers[].points[].x/y)
            flat_x = [x for marker_points in obstacles_x for x in marker_points]
            flat_y = [y for marker_points in obstacles_y for y in marker_points]

            if len(flat_x) == len(flat_y):
                self.obstacles.setData(flat_x, flat_y)
                print(f"Updated obstacles visualization with {len(flat_x)} points")
            else:
                self.obstacles.setData([], [])
                print(f"Obstacles coordinate mismatch: x={len(flat_x)}, y={len(flat_y)}")

        except Exception as e:
            print(f"Error updating obstacles visualization: {e}")
            self.obstacles.setData([], [])

    def _update_safety_lines_visualization(self) -> None:
        """Update safety lines visualization from 2D coordinate arrays."""
        try:
            safety_x = self._current_values.get('safety_lines_x', [])
            safety_y = self._current_values.get('safety_lines_y', [])

            if not safety_x or not safety_y:
                # Clear existing safety lines
                if hasattr(self, 'safety_lines'):
                    self.safety_lines.setData([], [])
                return

            # Flatten 2D arrays (markers[].points[].x/y)
            flat_x = [x for marker_points in safety_x for x in marker_points]
            flat_y = [y for marker_points in safety_y for y in marker_points]

            if len(flat_x) == len(flat_y):
                # Create safety lines plot item if it doesn't exist
                if not hasattr(self, 'safety_lines'):
                    self.safety_lines = pg.PlotDataItem(pen=pg.mkPen(color='orange', width=2))
                    self.plot.addItem(self.safety_lines)

                self.safety_lines.setData(flat_x, flat_y)
                print(f"Updated safety lines visualization with {len(flat_x)} points")
            else:
                if hasattr(self, 'safety_lines'):
                    self.safety_lines.setData([], [])
                print(f"Safety lines coordinate mismatch: x={len(flat_x)}, y={len(flat_y)}")

        except Exception as e:
            print(f"Error updating safety lines visualization: {e}")
            if hasattr(self, 'safety_lines'):
                self.safety_lines.setData([], [])

    def _update_border_visualization(self) -> None:
        """Update border visualization from 2D coordinate arrays."""
        try:
            border_x = self._current_values.get('border_x', [])
            border_y = self._current_values.get('border_y', [])

            if not border_x or not border_y:
                self.border_lines.setData([], [])
                return

            # Flatten 2D arrays (markers[].points[].x/y)
            flat_x = [x for marker_points in border_x for x in marker_points]
            flat_y = [y for marker_points in border_y for y in marker_points]

            if len(flat_x) == len(flat_y):
                self.border_lines.setData(flat_x, flat_y)
                print(f"Updated border visualization with {len(flat_x)} points")
            else:
                self.border_lines.setData([], [])
                print(f"Border coordinate mismatch: x={len(flat_x)}, y={len(flat_y)}")

        except Exception as e:
            print(f"Error updating border visualization: {e}")
            self.border_lines.setData([], [])

    def _initialize_ui(self) -> None:
        """Create all the UI components after the widget is properly positioned"""
        self.plot = pg.PlotWidget()
        self.plot.setBackground('#222222')  # Dark background to match app style

        # Disable the "A" button in the corner
        self.viewbox = self.plot.getViewBox()
        self.viewbox.setMenuEnabled(False)
        self.viewbox.setMouseEnabled(x=True, y=True)

        # Hide all axes but keep the grid functionality
        for axis in ['left', 'bottom', 'right', 'top']:
            self.plot.getAxis(axis).hide()

        # Create a custom grid with lines
        self.grid_lines = []
        grid_step = 10  # Grid spacing in meters
        grid_size = 200  # Grid extent from center (-grid_size to +grid_size)
        grid_pen = pg.mkPen(color=(0, 255, 0, 80), width=1, style=Qt.PenStyle.DotLine)

        # Add horizontal grid lines
        for y in range(-grid_size, grid_size + 1, grid_step):
            line = pg.InfiniteLine(pos=y, angle=0, pen=grid_pen)
            self.plot.addItem(line)
            self.grid_lines.append(line)

        # Add vertical grid lines
        for x in range(-grid_size, grid_size + 1, grid_step):
            line = pg.InfiniteLine(pos=x, angle=90, pen=grid_pen)
            self.plot.addItem(line)
            self.grid_lines.append(line)

        # Add coordinate display label that follows the mouse
        self.coord_label = pg.TextItem(text="", color=(255, 255, 100), anchor=(0, 0))
        self.coord_label.setZValue(100)  # Ensure it's on top of other elements
        font = QFont()
        font.setBold(True)
        self.coord_label.setFont(font)
        self.plot.addItem(self.coord_label)

        # Connect mouse move event to display coordinates
        self.proxy = pg.SignalProxy(self.plot.scene().sigMouseMoved, rateLimit=30, slot=self.mouse_moved)

        self.plot.setAspectLocked(True)

        # Remove padding around the plot
        self.plot.getViewBox().setDefaultPadding(0)

        # Add plot to layout (comes first for proper positioning)
        self.layout.addWidget(self.plot)

        # Add controls at the top - create a button panel using QGraphicsProxyWidget
        control_container = QWidget()
        control_container.setMaximumHeight(30)
        control_container.setStyleSheet("background-color: rgba(34, 34, 34, 180); border: none;")

        control_layout = QHBoxLayout(control_container)
        control_layout.setContentsMargins(5, 2, 5, 2)

        # Auto-center button
        self.auto_center_btn = QPushButton(self.tr("Auto Center: ON"))
        self.auto_center_btn.setStyleSheet(
            "background-color: #444444; color: white; border: 1px solid #666666;"
        )
        self.auto_center_btn.clicked.connect(self.toggle_auto_center)
        control_layout.addWidget(self.auto_center_btn)

        # Reset view button
        self.reset_view_btn = QPushButton(self.tr("Reset View"))
        self.reset_view_btn.setStyleSheet(
            "background-color: #444444; color: white; border: 1px solid #666666;"
        )
        self.reset_view_btn.clicked.connect(self.reset_view)
        control_layout.addWidget(self.reset_view_btn)

        # Add stretcher to push buttons to the left
        control_layout.addStretch(1)

        # Add button overlay to the plot
        self.button_proxy = self.plot.scene().addWidget(control_container)
        self.button_proxy.setPos(10, 10)

        # Add status container at the bottom with distance to hole
        status_container = QWidget()
        status_container.setMaximumHeight(25)
        status_container.setStyleSheet("background-color: rgba(34, 34, 34, 100); border: none;")

        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(10, 2, 10, 2)

        self.status_label = QLabel(self.tr("Distance to hole: -- m"))
        self.status_label.setStyleSheet("color: white; font-size: 10pt; background: transparent;")
        status_layout.addWidget(self.status_label)

        self.status_proxy = self.plot.scene().addWidget(status_container)

        # Position the status at the bottom of the plot
        rect = self.plot.rect()
        self.status_proxy.setPos(0, rect.height() - 25)
        self.status_proxy.widget().setFixedWidth(rect.width())

        # Create plot data items for all visualizations
        self.vehicle_outline = pg.PlotDataItem(pen=pg.mkPen(color='green', width=2))
        self.left_cat_outline = pg.PlotDataItem(pen=pg.mkPen(color='green', width=2))
        self.right_cat_outline = pg.PlotDataItem(pen=pg.mkPen(color='green', width=2))
        self.drill_marker_h = pg.PlotDataItem(pen=pg.mkPen(color='green', width=2))
        self.drill_marker_v = pg.PlotDataItem(pen=pg.mkPen(color='green', width=2))

        self.goal_outline = pg.PlotDataItem(pen=pg.mkPen(color=(255, 255, 0, 128), width=2))

        # For paths - full route is drawn below with dashed line
        self.full_route = pg.PlotDataItem(pen=pg.mkPen(color='cyan', width=1, style=Qt.PenStyle.DashLine))

        # Current maneuver/path segment draws on top with solid, more visible line
        self.planned_route = pg.PlotDataItem(pen=pg.mkPen(color='blue', width=3))

        # Current route segment (from current point to end) in different color
        self.current_route_line = pg.PlotDataItem(pen=pg.mkPen(color='orange', width=4))

        # For tailing, we'll manage individual items
        self.tailing_lines = []

        # For arrow visualization - single item approach for better performance
        self.arrows_x = []
        self.arrows_y = []
        self.arrow_plot = pg.PlotDataItem([], [], pen=pg.mkPen(color='white', width=1))
        self.plot.addItem(self.arrow_plot)

        self.border_lines = pg.PlotDataItem(pen=pg.mkPen(color='white', width=3))

        self.obstacles = pg.ScatterPlotItem(size=3, brush=pg.mkBrush('red'))

        # For holes, use scatter plot items for fast rendering
        self.finished_holes = pg.ScatterPlotItem(
            size=10,
            pen=pg.mkPen('g', width=1),
            brush=pg.mkBrush(0, 255, 0, 120),
            symbol='o'
        )
        self.active_hole = pg.ScatterPlotItem(
            size=10,
            pen=pg.mkPen('m', width=1.5),
            brush=pg.mkBrush(255, 0, 255, 180),
            symbol='o'
        )
        self.pending_holes = pg.ScatterPlotItem(
            size=10,
            pen=pg.mkPen(QColor(255, 128, 0), width=1),
            brush=pg.mkBrush(255, 128, 0, 120),
            symbol='o'
        )

        # For hole labels - only keep important ones
        self.hole_labels = []
        self.active_hole_id = None
        self.visible_labels_count = 1000  # Max labels to show

        # Property to track if we should show hole labels (based on zoom level)
        # Initialize to false to avoid showing labels at startup
        self.show_hole_labels = False

        # Tower to center distance already set in constructor

        # Cache for hole data
        self.hole_data = {}
        self.last_plan_update = 0

        # Camera management
        self.camera_target = (0, 0)
        self.camera_current = (0, 0)
        self.camera_smoothing = 0.5  # Higher value = faster response, less smoothing

        # Add items to plot in the right order (items added later appear on top)
        self.plot.addItem(self.full_route)  # Full route at the bottom
        self.plot.addItem(self.border_lines)
        self.plot.addItem(self.obstacles)
        self.plot.addItem(self.finished_holes)
        self.plot.addItem(self.active_hole)
        self.plot.addItem(self.pending_holes)
        self.plot.addItem(self.vehicle_outline)
        self.plot.addItem(self.left_cat_outline)
        self.plot.addItem(self.right_cat_outline)
        self.plot.addItem(self.drill_marker_h)
        self.plot.addItem(self.drill_marker_v)
        self.plot.addItem(self.planned_route)  # Current path segment on top
        self.plot.addItem(self.current_route_line)  # Current segment from current point
        self.plot.addItem(self.goal_outline)

        # Setup update timer for visualization
        self.update_timer = QTimer()
        self.update_timer.setInterval(100)  # 10 Hz update rate
        self.update_timer.timeout.connect(self.update_visualization)

        # Setup update timer for plan data (less frequent)
        self.plan_timer = QTimer()
        self.plan_timer.setInterval(2000)  # 0.5 Hz update rate - much less frequent
        self.plan_timer.timeout.connect(self.update_plan)

        # Auto center flag and zoom management
        self.auto_center = True
        self.current_vehicle_position = (0, 0)
        self.current_zoom_level = 50  # Default zoom range is ±50m

        # Add mouse wheel event to control zoom
        self.plot.wheelEvent = self.wheelEvent

        # Try to get the tower2center parameter
        self.update_tower2center()

    def update_tower2center(self) -> None:
        """Try to get the tower2center parameter from vehicle telemetry"""
        # For zenoh-based widgets, we could subscribe to a parameter topic
        # For now, keep the default value
        # TODO: Add parameter subscription when available
        pass

    def handle_driver_action_id(self, action_id: Any):
        """Handle driver action ID changes and request routes if needed."""
        try:
            old_action_id = getattr(self, 'current_action_id', None)
            self.current_action_id = action_id

            print(f"handle_driver_action_id called: old={old_action_id}, new={action_id}")

            # Проверяем нужно ли обновить routes
            # Вызываем сервис если:
            # 1. Action ID изменился
            # 2. Это первый раз когда мы получили action_id (old_action_id is None)
            # 3. У нас нет кэшированных routes
            should_call_service = (
                old_action_id != action_id or
                old_action_id is None or
                not hasattr(self, 'cached_routes') or
                not self.cached_routes
            )

            if should_call_service:
                print(f"Requesting routes: action_id={action_id}, reason: old={old_action_id}, has_cache={hasattr(self, 'cached_routes') and bool(getattr(self, 'cached_routes', []))}")
                # Request all service-based data sources when action ID changes
                service_sources = [src_name for src_name, cfg in self.data_sources.items()
                                 if cfg.get('service_name')]
                success_count = 0
                for source in service_sources:
                    if self.call_service_source(source):
                        success_count += 1
                        print(f"Successfully requested {source} for action ID: {action_id}")
                    else:
                        print(f"Failed to request {source}")

                if success_count > 0:
                    print(f"Successfully requested {success_count}/{len(service_sources)} service sources")
                else:
                    print("Failed to request any service sources")
            else:
                print(f"Action ID unchanged and routes cached: {action_id}")
        except Exception as e:
            print(f"Error handling driver action ID: {e}")
            import traceback
            traceback.print_exc()

    def handle_route_segments(self, value: Any):
        """Handle route segments data from service response."""
        try:
            print(f"handle_route_segments called with value type: {type(value)}")

            if value is None:
                print("Received None value for route segments")
                self.cached_routes = []
                self.planned_route.setData([], [])
                return

            if isinstance(value, (list, tuple)):
                self.cached_routes = value
                print(f"Received route segments: {len(value)} segments")

                # Debug: print structure of first segment if available
                if value and len(value) > 0:
                    first_segment = value[0]
                    print(f"First segment type: {type(first_segment)}")
                    if hasattr(first_segment, '__dict__'):
                        print(f"First segment attributes: {list(first_segment.__dict__.keys())}")
                    elif hasattr(first_segment, 'points'):
                        print(f"First segment has {len(first_segment.points)} points")

                # Обновляем planned route visualization на основе cached_routes
                self._update_planned_route_from_cached()
            else:
                print(f"Unexpected route segments type: {type(value)}")
                self.cached_routes = []
                # Очищаем visualization
                self.planned_route.setData([], [])

        except Exception as e:
            print(f"Error processing route segments: {e}")
            import traceback
            traceback.print_exc()
            self.cached_routes = []
            # Очищаем visualization при ошибке
            self.planned_route.setData([], [])

    def handle_full_route_x(self, value: Any):
        """Handle full route X coordinates from service response."""
        try:
            print(f"handle_full_route_x called with {len(value) if isinstance(value, (list, tuple)) else 'non-list'} values")
            # Data is automatically stored in _current_values and visualization is updated in _on_data_updated
        except Exception as e:
            print(f"Error processing full route X: {e}")

    def handle_full_route_y(self, value: Any):
        """Handle full route Y coordinates from service response."""
        try:
            print(f"handle_full_route_y called with {len(value) if isinstance(value, (list, tuple)) else 'non-list'} values")
            # Data is automatically stored in _current_values and visualization is updated in _on_data_updated
        except Exception as e:
            print(f"Error processing full route Y: {e}")

    def toggle_auto_center(self) -> None:
        """Toggle auto-centering on the vehicle"""
        self.auto_center = not self.auto_center
        status_text = self.tr("Auto Center: {0}").format(
            self.tr("ON") if self.auto_center else self.tr("OFF")
        )
        self.auto_center_btn.setText(status_text)

    def reset_view(self) -> None:
        """Reset the view to show the vehicle"""
        if hasattr(self, 'current_vehicle_position'):
            x, y = self.current_vehicle_position
            self.current_zoom_level = 15  # Reset zoom level
            self.show_hole_labels = True
            self.viewbox.setRange(xRange=(x-50, x+50), yRange=(y-50, y+50))
        else:
            self.viewbox.setRange(xRange=(-50, 50), yRange=(-50, 50))

    def start(self) -> None:
        """Start the widget and its update processes"""
        # Start zenoh subscriptions
        # super().start()

        # # Make sure parent is visible
        # if self.parent():
        #     self.parent().show()

        self.show()
        # self.raise_()  # Bring to front
        # self.activateWindow()  # Activate window

        self.update_timer.start()
        self.plan_timer.start()

        # Попытаемся запросить service sources при старте (если есть action_id)
        current_action = getattr(self, 'current_action_id', None)
        print(f"Map widget subscribe: current_action_id = {current_action}")

        if current_action is not None:
            print(f"Requesting service sources at startup for action ID: {current_action}")
            service_sources = [src_name for src_name, cfg in self.data_sources.items()
                             if cfg.get('service_name')]
            success_count = 0
            for source in service_sources:
                if self.call_service_source(source):
                    success_count += 1
                    print(f"Successfully requested {source} at startup for action ID: {current_action}")
                else:
                    print(f"Failed to request {source} at startup")

            if success_count > 0:
                print(f"Successfully requested {success_count}/{len(service_sources)} service sources at startup")
            else:
                print("Failed to request any service sources at startup")
        else:
            print("No action ID available at startup, skipping service requests")

    def update_visualization(self) -> None:
        """Update all visualization elements from zenoh data."""
        # Check if we have fresh position data (minimum required for vehicle visualization)
        position_fresh = (self.is_data_fresh('position_x') and
                         self.is_data_fresh('position_y') and
                         self.is_data_fresh('yaw'))

        if not position_fresh:
            return

        # Try to get the tower2center parameter
        self.update_tower2center()

        self.update_vehicle()
        # Note: Route, obstacles, safety lines, and borders are updated automatically
        # when data arrives via _on_data_updated() method
        self.update_goal_location()

        # Update hole labels if enabled at current zoom level
        # If turning off labels, clear existing ones
        if self.show_hole_labels:
            self.update_hole_labels()
        else:
            self.clear_hole_labels()

        # Update distance to hole in status bar
        self.update_distance_to_hole()

        # Center view on vehicle if auto-center is enabled with minimal smoothing
        if self.auto_center and hasattr(self, 'current_vehicle_position'):
            x, y = self.current_vehicle_position

            # Apply only light smoothing to reduce lag
            cx, cy = self.camera_current
            if cx == 0 and cy == 0:  # Initial position
                new_cx, new_cy = x, y
            else:
                new_cx = cx + (x - cx) * self.camera_smoothing
                new_cy = cy + (y - cy) * self.camera_smoothing

            self.camera_current = (new_cx, new_cy)

            # Check for NaN values before setting range
            if (math.isnan(new_cx) or math.isnan(new_cy) or
                math.isnan(self.current_zoom_level)):
                # Skip setting range if we have NaN values
                # print("Warning: NaN values detected in setRange parameters")
                return

            try:
                # Set the view range around the smoothed camera position
                self.viewbox.setRange(
                    xRange=(new_cx-self.current_zoom_level, new_cx+self.current_zoom_level),
                    yRange=(new_cy-self.current_zoom_level, new_cy+self.current_zoom_level),
                    padding=0
                )
            except Exception as e:
                print(f"Error setting view range: {e}")

    def get_active_vehid(self) -> Optional[str]:
        """Get the currently active vehicle ID.

        Returns:
            The vehicle ID or None if no vehicle is active
        """
        # For zenoh-based widgets, we use the current namespace
        return self.current_namespace

    def update_vehicle(self) -> None:
        """Update vehicle visualization from zenoh data"""
        try:
            x = self._current_values.get('position_x')
            y = self._current_values.get('position_y')
            yaw = self._current_values.get('yaw')

            if x is None or y is None:
                return

            self.current_vehicle_position = (x, y)

            # Vehicle contour - use configured polygon points
            if hasattr(self, 'vehicle_polygon') and self.vehicle_polygon:
                # Transform points to world coordinates
                x_points = []
                y_points = []
                for p in self.vehicle_polygon:
                    # Apply rotation and translation
                    px = p['x'] * math.cos(yaw) - p['y'] * math.sin(yaw) + x
                    py = p['x'] * math.sin(yaw) + p['y'] * math.cos(yaw) + y
                    x_points.append(px)
                    y_points.append(py)

                # Close the polygon
                x_points.append(x_points[0])
                y_points.append(y_points[0])
                self.vehicle_outline.setData(x_points, y_points)

            # Left caterpillar
            if hasattr(self, 'left_caterpillar_polygon') and self.left_caterpillar_polygon:
                x_points = []
                y_points = []
                for p in self.left_caterpillar_polygon:
                    px = p['x'] * math.cos(yaw) - p['y'] * math.sin(yaw) + x
                    py = p['x'] * math.sin(yaw) + p['y'] * math.cos(yaw) + y
                    x_points.append(px)
                    y_points.append(py)

                # Close the polygon
                x_points.append(x_points[0])
                y_points.append(y_points[0])
                self.left_cat_outline.setData(x_points, y_points)

            # Right caterpillar
            if hasattr(self, 'right_caterpillar_polygon') and self.right_caterpillar_polygon:
                x_points = []
                y_points = []
                for p in self.right_caterpillar_polygon:
                    px = p['x'] * math.cos(yaw) - p['y'] * math.sin(yaw) + x
                    py = p['x'] * math.sin(yaw) + p['y'] * math.cos(yaw) + y
                    x_points.append(px)
                    y_points.append(py)

                # Close the polygon
                x_points.append(x_points[0])
                y_points.append(y_points[0])
                self.right_cat_outline.setData(x_points, y_points)



            # Drill markers - positioned at tower_to_center_distance from vehicle center in forward direction
            drill_center_x = x + self.tower_to_center_distance * math.cos(yaw)
            drill_center_y = y + self.tower_to_center_distance * math.sin(yaw)

            # Horizontal line of the cross (perpendicular to vehicle orientation)
            x1 = drill_center_x - 1.0 * math.sin(yaw)
            y1 = drill_center_y + 1.0 * math.cos(yaw)
            x2 = drill_center_x + 1.0 * math.sin(yaw)
            y2 = drill_center_y - 1.0 * math.cos(yaw)
            self.drill_marker_h.setData([x1, x2], [y1, y2])

            # Vertical line of the cross (in the vehicle orientation)
            x1 = drill_center_x - 1.0 * math.cos(yaw)
            y1 = drill_center_y - 1.0 * math.sin(yaw)
            x2 = drill_center_x + 1.0 * math.cos(yaw)
            y2 = drill_center_y + 1.0 * math.sin(yaw)
            self.drill_marker_v.setData([x1, x2], [y1, y2])

        except Exception as e:
            print(f"Error updating vehicle: {e}")

    def update_routes(self) -> None:
        """Update route visualization."""
        try:
            # Обновляем planned route visualization на основе cached routes
            self._update_planned_route_from_cached()
        except Exception as e:
            print(f"Error updating routes: {e}")

    def _update_planned_route_from_cached(self) -> None:
        """Update planned route visualization from cached routes and current progress."""
        try:
            if not hasattr(self, 'cached_routes') or not self.cached_routes:
                self.planned_route.setData([], [])
                if hasattr(self, 'current_route_line'):
                    self.current_route_line.setData([], [])
                return

            print(f"Updating planned route from {len(self.cached_routes)} cached segments")

            # Get current progress
            current_segment_id = self._current_values.get('driver_segment_id')
            current_point_id = self._current_values.get('driver_point_id')

            # Draw ALL segments as planned route (blue)
            all_x_pts = []
            all_y_pts = []

            for segment in self.cached_routes:
                if hasattr(segment, 'points'):
                    for point in segment.points:
                        x, y = self._extract_point_coordinates(point)
                        if x is not None and y is not None:
                            all_x_pts.append(x)
                            all_y_pts.append(y)
                    # Add None to separate segments visually
                    if all_x_pts:
                        all_x_pts.append(None)
                        all_y_pts.append(None)

            # Remove last None if exists
            if all_x_pts and all_x_pts[-1] is None:
                all_x_pts = all_x_pts[:-1]
                all_y_pts = all_y_pts[:-1]

            self.planned_route.setData(all_x_pts, all_y_pts)

            # Draw current segment from current point to end in different color (orange)
            if (hasattr(self, 'current_route_line') and
                current_segment_id is not None and
                current_point_id is not None and
                current_segment_id < len(self.cached_routes)):

                current_segment = self.cached_routes[current_segment_id]
                if (hasattr(current_segment, 'points') and
                    current_point_id < len(current_segment.points)):

                    # Current segment from current point to end
                    current_x_pts = []
                    current_y_pts = []

                    for pt_idx in range(current_point_id, len(current_segment.points)):
                        point = current_segment.points[pt_idx]
                        x, y = self._extract_point_coordinates(point)
                        if x is not None and y is not None:
                            current_x_pts.append(x)
                            current_y_pts.append(y)

                    self.current_route_line.setData(current_x_pts, current_y_pts)
                    print(f"Updated route: {len(all_x_pts)} total points, current segment {current_segment_id} from point {current_point_id}")
                else:
                    self.current_route_line.setData([], [])
            elif hasattr(self, 'current_route_line'):
                self.current_route_line.setData([], [])

        except Exception as e:
            print(f"Error updating planned route from cache: {e}")
            self.planned_route.setData([], [])
            if hasattr(self, 'current_route_line'):
                self.current_route_line.setData([], [])

    def _extract_point_coordinates(self, point: Any) -> Tuple[Optional[float], Optional[float]]:
        """Universal coordinate extraction from different point structures."""
        try:
            # Handle different point structures
            if hasattr(point, 'x') and hasattr(point, 'y'):
                # Simple point structure
                return point.x, point.y
            elif hasattr(point, 'local_pose'):
                # drill_msgs/PathPoint structure
                if hasattr(point.local_pose, 'position'):
                    return point.local_pose.position.x, point.local_pose.position.y
                elif hasattr(point.local_pose, 'point_x'):
                    return point.local_pose.point_x, point.local_pose.point_y
            elif isinstance(point, dict):
                # Dictionary structure
                if 'x' in point and 'y' in point:
                    return point['x'], point['y']
                elif 'local_pose' in point:
                    pose = point['local_pose']
                    if 'position' in pose:
                        return pose['position']['x'], pose['position']['y']
                    elif 'point_x' in pose:
                        return pose['point_x'], pose['point_y']

            return None, None
        except Exception as e:
            print(f"Error extracting coordinates from point: {e}")
            return None, None

    def update_obstacles(self) -> None:
        """Update obstacles visualization from MarkerArray data"""
        try:
            markers = self._current_values.get('obstacles_vis', [])
            if markers:
                x_points = []
                y_points = []
                for marker in markers:
                    # Extract points from marker geometry
                    if hasattr(marker, 'points'):
                        for point in marker.points:
                            x_points.append(point.x)
                            y_points.append(point.y)
                    elif hasattr(marker, 'pose'):
                        # Single point marker
                        x_points.append(marker.pose.position.x)
                        y_points.append(marker.pose.position.y)
                    elif isinstance(marker, dict):
                        if 'points' in marker:
                            for point in marker['points']:
                                x_points.append(point['x'])
                                y_points.append(point['y'])
                        elif 'pose' in marker and 'position' in marker['pose']:
                            x_points.append(marker['pose']['position']['x'])
                            y_points.append(marker['pose']['position']['y'])

                self.obstacles.setData(x_points, y_points)
            else:
                self.obstacles.setData([], [])

        except Exception as e:
            print(f"Error updating obstacles: {e}")

    def update_tailing(self) -> None:
        """Update tailing visualization from MarkerArray data"""
        try:
            # Clear previous tailing lines by removing from plot instead of group
            for item in self.tailing_lines:
                self.plot.removeItem(item)
            self.tailing_lines = []

            # Add new tailing lines from MarkerArray
            markers = self._current_values.get('safety_berm_map', [])
            for marker in markers:
                if hasattr(marker, 'points') and len(marker.points) > 1:
                    # Line strip marker
                    for i in range(len(marker.points) - 1):
                        point = marker.points[i]
                        next_point = marker.points[i + 1]
                        line = pg.PlotDataItem(
                            [point.x, next_point.x],
                            [point.y, next_point.y],
                            pen=pg.mkPen(color='red', width=3)
                        )
                        self.plot.addItem(line)
                        self.tailing_lines.append(line)
                elif isinstance(marker, dict) and 'points' in marker and len(marker['points']) > 1:
                    # Dictionary format
                    for i in range(len(marker['points']) - 1):
                        point = marker['points'][i]
                        next_point = marker['points'][i + 1]
                        line = pg.PlotDataItem(
                            [point['x'], next_point['x']],
                            [point['y'], next_point['y']],
                            pen=pg.mkPen(color='red', width=3)
                        )
                        self.plot.addItem(line)
                        self.tailing_lines.append(line)

        except Exception as e:
            print(f"Error updating tailing: {e}")

    def update_borders(self) -> None:
        """Update border visualization from MarkerArray data"""
        try:
            markers = self._current_values.get('border_vis', [])
            if markers:
                x_points = []
                y_points = []
                for marker in markers:
                    if hasattr(marker, 'points'):
                        for point in marker.points:
                            x_points.append(point.x)
                            y_points.append(point.y)
                    elif isinstance(marker, dict) and 'points' in marker:
                        for point in marker['points']:
                            x_points.append(point['x'])
                            y_points.append(point['y'])

                if x_points and y_points:
                    # Close the polygon
                    x_points.append(x_points[0])
                    y_points.append(y_points[0])
                    self.border_lines.setData(x_points, y_points)
                else:
                    self.border_lines.setData([], [])
            else:
                self.border_lines.setData([], [])

        except Exception as e:
            print(f"Error updating borders: {e}")

    def update_goal_location(self) -> None:
        """Update goal location visualization"""
        try:
            # Get next hole location from core
            if hasattr(self.core, 'next_hole_location') and self.core.next_hole_location:
                next_hole = self.core.next_hole_location
                hole_x = next_hole.get('x')
                hole_y = next_hole.get('y')
                hole_yaw = next_hole.get('azimuth')

                if hole_x is not None and hole_y is not None:
                    # Get vehicle polygon for the goal outline
                    if hasattr(self, 'vehicle_polygon') and self.vehicle_polygon:
                        # Transform points to goal location coordinates
                        x_points = []
                        y_points = []
                        for p in self.vehicle_polygon:
                            # Apply rotation and translation, but shift by -tower_to_center_distance in goal's forward direction
                            px = p['x'] * math.cos(hole_yaw) - p['y'] * math.sin(hole_yaw) + hole_x - self.tower_to_center_distance * math.cos(hole_yaw)
                            py = p['x'] * math.sin(hole_yaw) + p['y'] * math.cos(hole_yaw) + hole_y - self.tower_to_center_distance * math.sin(hole_yaw)
                            x_points.append(px)
                            y_points.append(py)

                        # Close the polygon
                        if x_points and y_points:
                            x_points.append(x_points[0])
                            y_points.append(y_points[0])
                            self.goal_outline.setData(x_points, y_points)

        except Exception as e:
            print(f"Error updating goal location: {e}")

    def update_plan(self) -> None:
        """Get plan data from core.plan which is populated by PlanReader"""
        try:
            # Only update if enough time has passed
            current_time = time.time()
            if current_time - self.last_plan_update < 1.0:  # Minimum 1 second between plan updates
                return

            vehid = self.get_active_vehid()
            if vehid is None:
                return

            # Check if we have plan data from PlanReader
            if hasattr(self.core, 'plan') and vehid in self.core.plan:
                plan = self.core.plan[vehid]

                # Get active hole ID from core
                active_hole = None
                if hasattr(self.core, 'next_hole_location') and self.core.next_hole_location:
                    for hole in plan.get('holes', []):
                        if hole.get('location') == self.core.next_hole_location:
                            active_hole = hole.get('holeid')
                            break

                # Update hole visualization only if plan or active hole changed
                if "holes" in plan and (self.hole_data.get('holes') != plan["holes"] or
                                        self.active_hole_id != active_hole):
                    # Update hole visualization
                    self.update_holes(plan["holes"], active_hole)
                    # Cache data to avoid unnecessary updates
                    self.hole_data['holes'] = plan["holes"]
                    self.active_hole_id = active_hole
                    self.last_plan_update = current_time

        except Exception as e:
            print(f"Error updating plan visualization: {e}")

    def update_holes(self, holes: List[Dict[str, Any]], active_hole: Optional[str]) -> None:
        """Update the visualization of holes using efficient scatter plots"""
        try:
            # Prepare data for scatter plots (using separate scatter items for different hole types)
            finished_x, finished_y = [], []
            active_x, active_y = [], []
            pending_x, pending_y = [], []

            # Prepare arrays for arrow plots (more efficient than individual lines)
            self.arrows_x = []
            self.arrows_y = []

            # Clear existing labels
            for label in self.hole_labels:
                self.plot.removeItem(label)
            self.hole_labels = []

            # Prepare hole info for label update
            hole_info = []

            # Process all holes
            for hole in holes:
                try:
                    # Basic hole properties
                    x = hole['location']['x']
                    y = hole['location']['y']
                    azimuth = hole['location']['azimuth']

                    # Save info for labels
                    hole_info.append({
                        'x': x,
                        'y': y,
                        'name': hole['name'],
                        'depth': hole['depth'],
                        'elevation': hole.get('elevation'),
                        'is_active': hole['holeid'] == active_hole,
                        'is_finished': hole['is_finished']
                    })

                    # Add to appropriate scatter plot data
                    if hole['is_finished']:
                        finished_x.append(x)
                        finished_y.append(y)
                    elif hole['holeid'] == active_hole:
                        active_x.append(x)
                        active_y.append(y)
                        # Set next hole location in core
                        self.core.next_hole_location = hole['location']
                    else:
                        pending_x.append(x)
                        pending_y.append(y)

                    # Add arrow for direction indication - add to batch array
                    if not hole['is_finished']:
                        arrow_end_x = x + 0.3 * math.cos(azimuth)
                        arrow_end_y = y + 0.3 * math.sin(azimuth)

                        # Add line segment to arrays
                        self.arrows_x.extend([x, arrow_end_x, float('nan')])  # nan for line breaks
                        self.arrows_y.extend([y, arrow_end_y, float('nan')])

                except Exception as e:
                    print(f"Error processing hole: {e}")

            # Update the scatter plots in a batch (very efficient)
            self.finished_holes.setData(finished_x, finished_y)
            self.active_hole.setData(active_x, active_y)
            self.pending_holes.setData(pending_x, pending_y)

            # Update all arrows in a single batch
            self.arrow_plot.setData(self.arrows_x, self.arrows_y)

            # Save hole info for label updates
            self.hole_info = hole_info

            self.update_hole_labels()

        except Exception as e:
            print(f"Error updating holes: {e}")

    def update_hole_labels(self) -> None:
        """Update only visible hole labels based on current view and distance to vehicle"""
        try:
            if not hasattr(self, 'hole_info') or not hasattr(self, 'current_vehicle_position'):
                return

            # Get current vehicle position and viewbox range
            veh_x, veh_y = self.current_vehicle_position

            view_range = self.viewbox.viewRange()
            view_rect = QRectF(
                float(view_range[0][0]), float(view_range[1][0]),  # x, y
                float(view_range[0][1] - view_range[0][0]),  # width
                float(view_range[1][1] - view_range[1][0])   # height
            )

            # Clear existing labels
            for label in self.hole_labels:
                self.plot.removeItem(label)
            self.hole_labels = []

            # Sort holes by distance to vehicle
            sorted_holes = sorted(
                self.hole_info,
                key=lambda h: (h['x'] - veh_x)**2 + (h['y'] - veh_y)**2
            )

            # Show only closest holes within view
            visible_count = 0
            for hole in sorted_holes:
                # Check if hole is in view
                if not view_rect.contains(hole['x'], hole['y']):
                    continue

                # Limit max visible labels
                if visible_count >= self.visible_labels_count and not hole['is_active']:
                    continue

                # Create text content
                text = str(hole['name']) + '\n' + "%.1f" % hole['depth']
                if hole['elevation']:
                    text += "\nincl. " + str(int(hole['elevation']))

                # Create label with appropriate size and color
                if hole['is_active']:
                    # Active hole gets larger, more prominent text
                    label = pg.TextItem(
                        text=text,
                        color='white',
                        anchor=(0, 0)
                    )
                    label.setPos(hole['x'] - 0.8, hole['y'] - 0.6)
                    font = QFont()
                    font.setBold(True)
                    label.setFont(font)
                elif hole['is_finished']:
                    # Finished holes get smaller, greenish text
                    label = pg.TextItem(
                        text=text,
                        color=(200, 255, 200),
                        anchor=(0, 0)
                    )
                    label.setPos(hole['x'] - 0.6, hole['y'] - 0.4)
                    font = QFont()
                    font.setPointSize(7)
                    label.setFont(font)
                else:
                    # Pending holes get medium orange text
                    label = pg.TextItem(
                        text=text,
                        color=(255, 180, 100),
                        anchor=(0, 0)
                    )
                    label.setPos(hole['x'] - 0.7, hole['y'] - 0.5)
                    font = QFont()
                    font.setPointSize(8)
                    label.setFont(font)

                self.plot.addItem(label)
                self.hole_labels.append(label)
                visible_count += 1

        except Exception as e:
            print(f"Error updating hole labels: {e}")

    def update_distance_to_hole(self) -> None:
        """Update the distance to hole in the status label"""
        if not hasattr(self, 'status_label'):
            return

        if not hasattr(self.core, 'next_hole_location') or self.core.next_hole_location is None:
            self.status_label.setText(self.tr("Distance to hole: -- m"))
            return

        # Get vehicle position and orientation from zenoh data
        pos_x = self._current_values.get('position_x')
        pos_y = self._current_values.get('position_y')
        yaw = self._current_values.get('yaw')

        if pos_x is None or pos_y is None or yaw is None:
            return

        # Adjust position to drill location (using tower_to_center_distance)
        drill_x = pos_x + self.tower_to_center_distance * math.cos(yaw)
        drill_y = pos_y + self.tower_to_center_distance * math.sin(yaw)

        # Get hole location
        hole_x = self.core.next_hole_location.get('x')
        hole_y = self.core.next_hole_location.get('y')

        if hole_x is None or hole_y is None:
            return

        # Calculate distance
        distance = math.sqrt((drill_x - hole_x)**2 + (drill_y - hole_y)**2)

        # Update status label
        self.status_label.setText(
            self.tr("Distance to hole: {0:.2f} m").format(distance)
        )

    def wheelEvent(self, event) -> None:
        """Handle mouse wheel events for zooming.

        Args:
            event: The wheel event
        """
        # Get delta and adjust zoom level (scale factor proportional to current zoom)
        delta = event.angleDelta().y() / 120
        factor = 0.8 if delta < 0 else 1.25

        # Apply zoom to current level
        self.current_zoom_level /= factor

        # Clamp zoom to reasonable limits (5m to 500m)
        self.current_zoom_level = max(5, min(500, self.current_zoom_level))

        # Update show_hole_labels based on zoom level and clear labels if needed
        self.show_hole_labels = self.current_zoom_level <= 20  # Show labels only when zoomed in

        try:
            # If not auto-centered, zoom around current center, not vehicle
            if not self.auto_center:
                # Get current center
                view_range = self.viewbox.viewRange()
                center_x = (view_range[0][0] + view_range[0][1]) / 2
                center_y = (view_range[1][0] + view_range[1][1]) / 2

                # Check for NaN values
                if math.isnan(center_x) or math.isnan(center_y) or math.isnan(self.current_zoom_level):
                    # print("Warning: NaN values detected in wheelEvent")
                    return

                # Apply zoom around current center
                self.viewbox.setRange(
                    xRange=(center_x-self.current_zoom_level, center_x+self.current_zoom_level),
                    yRange=(center_y-self.current_zoom_level, center_y+self.current_zoom_level)
                )
            else:
                # When auto-centered, zoom around the vehicle
                if hasattr(self, 'current_vehicle_position'):
                    x, y = self.current_vehicle_position

                    # Check for NaN values
                    if math.isnan(x) or math.isnan(y) or math.isnan(self.current_zoom_level):
                        # print("Warning: NaN values detected in wheelEvent")
                        return

                    self.viewbox.setRange(
                        xRange=(x-self.current_zoom_level, x+self.current_zoom_level),
                        yRange=(y-self.current_zoom_level, y+self.current_zoom_level)
                    )
        except Exception as e:
            print(f"Error in wheelEvent: {e}")

    def resizeEvent(self, event) -> None:
        """Handle resize events to reposition status label"""
        super().resizeEvent(event)
        # Update the status position
        if hasattr(self, 'status_proxy') and hasattr(self, 'plot'):
            rect = self.plot.rect()
            self.status_proxy.setPos(0, int(rect.height()) - 25)
            self.status_proxy.widget().setFixedWidth(int(rect.width()))

    def clear_hole_labels(self) -> None:
        """Remove all hole labels from the plot"""
        for label in self.hole_labels:
            self.plot.removeItem(label)
        self.hole_labels = []

    def transform_point(self, x: float, y: float, yaw: float, dx: float, dy: float) -> Tuple[float, float]:
        """Transform a point by rotation and translation"""
        # Rotate
        rot_x = dx * math.cos(yaw) - dy * math.sin(yaw)
        rot_y = dx * math.sin(yaw) + dy * math.cos(yaw)

        # Translate
        return x + rot_x, y + rot_y

    def mouse_moved(self, event) -> None:
        """Update the coordinate display when mouse moves over the plot"""
        try:
            pos = event[0]
            if self.plot.sceneBoundingRect().contains(pos):
                # Convert scene coordinates to view coordinates
                mouse_point = self.viewbox.mapSceneToView(pos)
                # Update text with coordinates
                self.coord_label.setText(f"X: {mouse_point.x():.1f}, Y: {mouse_point.y():.1f}")
                # Position the label near the cursor (with offset)
                view_pos = self.viewbox.mapSceneToView(pos + QPointF(10, 10))
                self.coord_label.setPos(view_pos)
            else:
                # Hide text when mouse is outside the plot
                self.coord_label.setText("")
        except Exception:
            # Silently ignore errors in coordinate display
            pass

    def kill(self) -> None:
        """Clean up resources when widget is no longer needed."""
        if self.killed:
            return

        # Stop timers
        if self.update_timer and self.update_timer.isActive():
            self.update_timer.stop()
        if self.plan_timer and self.plan_timer.isActive():
            self.plan_timer.stop()

        # Clean up zenoh subscriptions
        self.cleanup()

        self.killed = True


class Map:
    """Class for initializing the map widget.

    This class serves as a factory for creating and configuring MapWidget instances.
    """
    def __init__(self, core: Any) -> None:
        """Initialize the Map factory.

        Args:
            core: The core application object
        """
        self.core = core
        self.widget: Optional[MapWidget] = None

    def prepare(self, width: int = 500, height: int = 500, x: int = 500, y: int = 800, screen_num: int = 1, data_sources: Dict = None) -> None:
        """Prepare the map widget with the specified parameters.

        Args:
            width: Width of the widget
            height: Height of the widget
            x: X position of the widget
            y: Y position of the widget
            screen_num: Screen number (0 for sensor screen, 1+ for main screen)
            data_sources: Dictionary of data source configurations for zenoh subscriptions
        """
        self.widget = MapWidget(self.core)
        self.widget.prepare(width, height, x, y, screen_num, data_sources)

    def start(self) -> None:
        """Start the map widget."""
        if self.widget:
            self.widget.start()

    def kill(self) -> None:
        """Clean up the map widget."""
        if self.widget:
            self.widget.kill()