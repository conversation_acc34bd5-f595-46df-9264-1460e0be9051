# -*- coding: utf-8 -*-

from __future__ import annotations

import time
import logging
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple

from PyQt6.QtCore import QTimer, pyqtSignal
from PyQt6.QtWidgets import QWidget

__all__ = ["ZenohWidget"]

logger = logging.getLogger("ZenohWidget")

class ZenohWidget(QWidget):
    """Base class for GUI widgets consuming Zenoh data."""

    # ---------------------------------------------------- Qt signals
    data_updated = pyqtSignal(str, object)        # source_name, value
    data_freshness_changed = pyqtSignal(str, bool)  # source_name, is_fresh

    # ---------------------------------------------------- construction
    def __init__(
        self,
        core: Any,
        *args,
        data_timeout: float = 5.0,
        fixed_namespace: Optional[str] = None,
        **kwargs,
    ) -> None:
        super().__init__(*args, **kwargs)
        self.core = core
        self.zenoh = getattr(core, "zenoh_client", None)

        self.data_sources: Dict[str, Dict[str, Any]] = {}
        self._handles: List[Any] = []            # SubscriptionHandle list
        self.current_namespace: Optional[str] = None

        # Service-related data
        self._service_cache: Dict[str, Any] = {}  # Cache for service responses
        self._service_timers: Dict[str, QTimer] = {}  # Periodic service call timers

        # "Sticky" namespace support ------------------------------
        # If *fixed_namespace* is provided, the widget will always
        # subscribe to that namespace and ignore global vehicle
        # changes coming from *core.vehicle_changed*.
        self.fixed_namespace: Optional[str] = fixed_namespace

        # Freshness tracking --------------------------------------
        self.data_timeout = data_timeout
        self.last_update_time: Dict[str, float] = {}
        self.data_is_fresh: Dict[str, bool] = {}
        self.freshness_timer = QTimer(self)
        self.freshness_timer.timeout.connect(self._check_data_freshness)

        # React to namespace changes ---------------------------------
        # Widgets with fixed namespace do not need to listen for
        # global vehicle changes.
        if self.fixed_namespace is None and hasattr(self.core, "vehicle_changed"):
            self.core.vehicle_changed.connect(self.on_namespace_changed)

        # Each widget may implement specialised handlers -------------
        self.data_updated.connect(self._dispatch_update)

    # ---------------------------------------------------- namespace helpers
    def _get_current_namespace(self) -> Optional[str]:
        """Return the namespace this widget should use.

        If *fixed_namespace* was specified at construction/prepare time,
        that value is returned unconditionally, effectively making the
        widget "sticky" to a particular vehicle. Otherwise the namespace
        is resolved from the *core* object as before.
        """
        if self.fixed_namespace is not None:
            return self.fixed_namespace

        in_rc = getattr(self.core, "in_rc_vehid", None)
        watched = getattr(self.core, "watched_vehid", None)
        ns = in_rc or watched
        if ns and hasattr(self.core, "veh_active_states"):
            if not self.core.veh_active_states.get(ns, False):
                ns = None
        return ns

    def on_namespace_changed(self):
        new_ns = self._get_current_namespace()
        if new_ns == self.current_namespace:
            return
        self.unsubscribe()
        self.current_namespace = new_ns
        if self.current_namespace and self.zenoh:
            self.subscribe()
        # mark all sources stale so UI can grey‑out
        for src in self.data_sources:
            self._set_data_freshness(src, False)

    # ---------------------------------------------------- runtime API
    def prepare(self, *,
                data_sources: Optional[Dict[str, Dict[str, Any]]] = None,
                data_timeout: Optional[float] = None,
                fixed_namespace: Optional[str] = None,
                **_: Any):
        if data_sources is not None:
            self.data_sources = data_sources
            for src in data_sources:
                self.data_is_fresh[src] = False

        if data_timeout is not None:
            self.data_timeout = data_timeout

        # Allow supplying fixed namespace via *prepare()* as well
        # (overrides constructor value).
        if fixed_namespace is not None:
            self.fixed_namespace = fixed_namespace
            # Stop listening to global namespace changes if we switch
            # to sticky behaviour at runtime.
            if hasattr(self.core, "vehicle_changed"):
                try:
                    self.core.vehicle_changed.disconnect(self.on_namespace_changed)
                except Exception:
                    pass



    def start(self):
        self.current_namespace = self._get_current_namespace()
        if self.current_namespace and self.zenoh:
            self.subscribe()
        self.freshness_timer.start(1000)

    def cleanup(self):
        """Clean up resources when widget is destroyed."""
        logger.debug("Cleaning up ZenohWidget resources")

        # Stop freshness timer
        if hasattr(self, 'freshness_timer') and self.freshness_timer.isActive():
            self.freshness_timer.stop()

        # Unsubscribe from topics and stop service timers
        self.unsubscribe()

        # Disconnect from zenoh signals
        if hasattr(self, '_connected_zenoh_id') and self.zenoh:
            try:
                self.zenoh.serviceResponseReceived.disconnect(self._on_service_response)
            except Exception:
                pass  # Already disconnected
            delattr(self, '_connected_zenoh_id')

    # ---------------------------------------------------- field extraction
    def _extract_field_value(self, data: Any, field_path: str) -> Any:
        """Extract value from nested data by field path.

        Supports:
        - Dotted paths for nested fields (e.g. "pose.position.x")
        - Array iteration with [] suffix (e.g. "points[].x")
        - Multi-dimensional arrays (e.g. "segments[].points[].x" returns 2D array)

        Args:
            data: Source data (message object, dict, or list)
            field_path: Dotted path with optional [] for arrays

        Returns:
            Extracted value:
            - Single value for simple paths
            - 1D list for single [] iteration
            - 2D list for double [] iteration (preserves structure)
        """
        logger.debug(f"Extracting field '{field_path}' from data type: {type(data)}")

        if not field_path:
            return data

        parts = field_path.split(".")

        # Count array levels for return type determination
        array_count = sum(1 for part in parts if part.endswith("[]"))
        logger.debug(f"Array count: {array_count}")

        # Handle multi-dimensional arrays specially
        if array_count >= 2:
            logger.debug(f"Using {array_count}D array extraction")
            return self._extract_multidimensional_array(data, field_path)

        # Process path step by step
        current = data

        for part in parts:
            if current is None:
                logger.debug(f"Data is None at part: {part}")
                break

            if part.endswith("[]"):
                # Array iteration
                field_name = part[:-2]
                logger.debug(f"Array iteration on field: {field_name}")

                # Get the array field
                if isinstance(current, dict):
                    array_data = current.get(field_name, [])
                else:
                    try:
                        array_data = getattr(current, field_name, [])
                    except AttributeError:
                        logger.debug(f"Field '{field_name}' not found in {type(current)}")
                        array_data = []

                # Ensure it's a list
                if not isinstance(array_data, (list, tuple)):
                    array_data = [array_data] if array_data is not None else []

                current = list(array_data)
                logger.debug(f"Array data: {len(current)} items")

            else:
                # Simple field access
                logger.debug(f"Field access: {part}")
                if isinstance(current, (list, tuple)):
                    # Apply to each item in list
                    new_data = []
                    for item in current:
                        if item is None:
                            continue

                        if isinstance(item, dict):
                            val = item.get(part)
                        else:
                            try:
                                val = getattr(item, part)
                            except AttributeError:
                                logger.debug(f"Field '{part}' not found in {type(item)}")
                                continue

                        if val is not None:
                            new_data.append(val)

                    current = new_data
                    logger.debug(f"Field access on list: {len(current)} items")
                else:
                    # Single item
                    if isinstance(current, dict):
                        current = current.get(part)
                    else:
                        try:
                            current = getattr(current, part)
                        except AttributeError:
                            logger.debug(f"Field '{part}' not found in {type(current)}")
                            current = None

                    logger.debug(f"Field access result: {type(current)}")

        # Return appropriate type based on array count
        if array_count == 0:
            logger.debug(f"Returning single value: {type(current)}")
            return current
        else:
            result = current if isinstance(current, list) else ([] if current is None else [current])
            logger.debug(f"Returning list with {len(result)} items")
            return result

    def _extract_multidimensional_array(self, data: Any, field_path: str) -> Any:
        """Extract multi-dimensional array preserving structure for paths with multiple [].

        Supports any number of dimensions:
        - segments[].points[].x → 2D: [[x1, x2], [x3, x4, x5], [x6]]
        - tasks[].segments[].points[].x → 3D: [[[x1, x2], [x3]], [[x4, x5, x6]]]

        Args:
            data: Source data
            field_path: Path with multiple [] levels

        Returns:
            Multi-dimensional list preserving structure at each array level
        """
        logger.debug(f"Extracting multi-dimensional array for path: {field_path}")

        parts = field_path.split(".")

        # Find all array levels
        array_indices = []
        for i, part in enumerate(parts):
            if part.endswith("[]"):
                array_indices.append(i)

        if not array_indices:
            # No arrays, fallback to simple extraction
            return self._extract_field_value(data, field_path)

        logger.debug(f"Array levels at indices: {array_indices}")

        # Process recursively, handling one array level at a time
        return self._extract_recursive_array(data, parts, array_indices, 0)

    def _extract_recursive_array(self, data: Any, parts: List[str], array_indices: List[int], current_array_idx: int) -> Any:
        """Recursively extract arrays preserving structure at each level.

        Args:
            data: Current data to process
            parts: All path parts
            array_indices: Indices of parts that are arrays
            current_array_idx: Index in array_indices we're currently processing

        Returns:
            Nested list structure
        """
        if current_array_idx >= len(array_indices):
            # No more array levels, extract remaining path normally
            remaining_parts = parts[array_indices[-1] + 1:] if array_indices else parts
            remaining_path = ".".join(remaining_parts)

            if remaining_path:
                return self._extract_simple_path(data, remaining_path)
            else:
                return data

        # Get the current array level index
        array_part_idx = array_indices[current_array_idx]

        # Extract up to current array level
        current_data = data
        for i in range(array_part_idx):
            part = parts[i]
            if isinstance(current_data, dict):
                current_data = current_data.get(part)
            else:
                try:
                    current_data = getattr(current_data, part)
                except AttributeError:
                    return []

            if current_data is None:
                return []

        # Get the array field
        array_field = parts[array_part_idx][:-2]  # Remove []

        if isinstance(current_data, dict):
            array_data = current_data.get(array_field, [])
        else:
            try:
                array_data = getattr(current_data, array_field, [])
            except AttributeError:
                array_data = []

        if not isinstance(array_data, (list, tuple)):
            array_data = [array_data] if array_data is not None else []

        # Process each item in the array
        result = []
        for item in array_data:
            if item is None:
                result.append([])
                continue

            # Build remaining parts after current array
            remaining_parts = parts[array_part_idx + 1:]
            remaining_array_indices = [idx - array_part_idx - 1 for idx in array_indices[current_array_idx + 1:]]

            # Recursively process the remaining path
            item_result = self._extract_recursive_array(item, remaining_parts, remaining_array_indices, 0)
            result.append(item_result)

        logger.debug(f"Array level {current_array_idx}: {len(result)} items")
        return result

    def _extract_simple_path(self, data: Any, field_path: str) -> Any:
        """Extract simple path without arrays."""
        if not field_path:
            return data

        current = data
        for part in field_path.split("."):
            if current is None:
                return None

            if isinstance(current, dict):
                current = current.get(part)
            else:
                try:
                    current = getattr(current, part)
                except AttributeError:
                    return None

        return current



    # ---------------------------------------------------- subscription logic
    def subscribe(self):
        if not self.zenoh or not self.current_namespace:
            logger.warning(f"Cannot subscribe: zenoh={bool(self.zenoh)}, ns={self.current_namespace}")
            return
        self.unsubscribe()  # safety first

        # Clear service cache when changing namespace
        self._service_cache.clear()

        # Group topic-based sources
        grouped: Dict[Tuple[str, str], List[Tuple[str, str, float]]] = defaultdict(list)

        for src_name, cfg in self.data_sources.items():
            # Handle service-based sources
            if "service_name" in cfg:
                self._setup_service_source(src_name, cfg)
                continue

            # Handle topic-based sources
            try:
                grouped[(cfg["topic"], cfg["msg_type"])].append(
                    (src_name, cfg["field"], cfg.get("multiplier", 1.0))
                )
            except KeyError as e:
                logger.error(f"Incomplete cfg for {src_name}: missing {e}")

        for (topic, msg_type), fields in grouped.items():
            logger.debug(f"Subscribing to {topic} ({msg_type}) in namespace {self.current_namespace}")

            def _handler(_key: str, msg: object, *, _fields=fields):
                logger.debug(f"Received message on {_key}: {msg}")
                for s_name, field_path, k in _fields:
                    try:
                        # Extract nested field value using new method
                        val = self._extract_field_value(msg, field_path)

                        # Apply multiplier to numeric values
                        if k != 1.0:
                            if isinstance(val, (int, float)):
                                val = val * k
                            elif isinstance(val, list) and val:
                                # Apply multiplier to numeric lists
                                if all(isinstance(v, (int, float)) for v in val):
                                    val = [v * k for v in val]

                        logger.debug(f"  - Extracted field '{field_path}' for source '{s_name}': {val}")
                        self.data_updated.emit(s_name, val)
                        self.last_update_time[s_name] = time.time()
                        if not self.data_is_fresh.get(s_name):
                            self._set_data_freshness(s_name, True)
                    except Exception as e:
                        logger.error(f"Cannot extract field '{field_path}' from message: {e}")

            handle = self.zenoh.subscribe(topic, msg_type, self.current_namespace, callback=_handler)
            self._handles.append(handle)

    def unsubscribe(self):
        if not self.zenoh:
            return
        for h in self._handles:
            try:
                self.zenoh.unsubscribe(h)
            except Exception as e:
                logger.error(f"Error during unsubscribe: {e}")
        self._handles.clear()

        # Stop and clear service timers
        for timer in self._service_timers.values():
            timer.stop()
        self._service_timers.clear()

    # ---------------------------------------------------- service sources
    def _setup_service_source(self, src_name: str, cfg: Dict[str, Any]) -> None:
        """Setup a service-based data source.

        Args:
            src_name: Name of the data source
            cfg: Configuration containing service_name, service_pkg, service_type, field, etc.
        """
        logger.debug(f"Setting up service source '{src_name}' for service {cfg['service_name']}")

        # Setup periodic calling if interval is specified
        interval = cfg.get("call_interval_sec")
        if interval and interval > 0:
            timer = QTimer(self)
            # Create a closure that captures values by value, not by reference
            def timer_callback(source_name=src_name, source_cfg=cfg):
                self._call_service_for_source(source_name, source_cfg)
            timer.timeout.connect(timer_callback)
            timer.start(int(interval * 1000))  # Convert to milliseconds
            self._service_timers[src_name] = timer
            logger.debug(f"Service '{src_name}' will be called every {interval} seconds")

        # Note: Service is NOT called automatically on setup
        # Widget code should call call_service_source() when needed

    def call_service_source(self, src_name: str, **kwargs) -> bool:
        """Call a configured service source manually.

        Args:
            src_name: Name of the service source from data_sources
            **kwargs: Additional request parameters

        Returns:
            True if service call was initiated, False otherwise
        """
        if src_name not in self.data_sources:
            logger.error(f"Service source '{src_name}' not configured")
            return False

        cfg = self.data_sources[src_name]
        if "service_name" not in cfg:
            logger.error(f"Source '{src_name}' is not a service source")
            return False

        return self._call_service_for_source(src_name, cfg, **kwargs)

    def _ensure_service_response_connected(self) -> None:
        """Ensure service response handler is connected to current zenoh client."""
        if not self.zenoh:
            return

        # Check if we need to connect/reconnect
        current_zenoh_id = id(self.zenoh)
        if not hasattr(self, '_connected_zenoh_id') or self._connected_zenoh_id != current_zenoh_id:
            # Disconnect from old zenoh client if exists
            if hasattr(self, '_connected_zenoh_id'):
                logger.debug("Reconnecting service response handler to new zenoh client")

            try:
                self.zenoh.serviceResponseReceived.connect(self._on_service_response)
                self._connected_zenoh_id = current_zenoh_id
                logger.debug("Service response handler connected")
            except Exception as e:
                logger.error(f"Failed to connect service response handler: {e}")

    def _call_service_for_source(self, src_name: str, cfg: Dict[str, Any], **kwargs) -> bool:
        """Call a service and process the response for a data source.

        Args:
            src_name: Name of the data source
            cfg: Service configuration
            **kwargs: Additional request parameters

        Returns:
            True if service call was initiated, False otherwise
        """
        if not self.zenoh:
            # For periodic calls, log less frequently to avoid spam
            if cfg.get("call_interval_sec"):
                logger.debug(f"Cannot call periodic service for {src_name}: no Zenoh client")
            else:
                logger.warning(f"Cannot call service for {src_name}: no Zenoh client")
            return False

        if not self.current_namespace:
            # For periodic calls, log less frequently to avoid spam
            if cfg.get("call_interval_sec"):
                logger.debug(f"Cannot call periodic service for {src_name}: no namespace")
            else:
                logger.warning(f"Cannot call service for {src_name}: no namespace")
            return False

        try:
            # Call service asynchronously
            logger.debug(f"Calling service {cfg['service_name']} with type {cfg['service_pkg']}/srv/{cfg['service_type']} in namespace {self.current_namespace}")

            # Check if zenoh client has the method
            if not hasattr(self.zenoh, 'call_service_async'):
                logger.error(f"ZenohClient does not have call_service_async method")
                return False

            self.zenoh.call_service_async(
                service_name=cfg["service_name"],
                service_pkg=cfg["service_pkg"],
                service_type=cfg["service_type"],
                namespace=self.current_namespace,
                **kwargs
            )

            # Connect response handler (ensure it's connected to current zenoh client)
            self._ensure_service_response_connected()

            return True

        except AttributeError as e:
            logger.error(f"ZenohClient method error for {src_name}: {e}")
            return False
        except Exception as e:
            # For periodic calls, log less frequently to avoid spam
            if cfg.get("call_interval_sec"):
                logger.debug(f"Error calling periodic service for {src_name}: {e}")
            else:
                logger.error(f"Error calling service for {src_name}: {e}")
            return False

    def _on_service_response(self, service_name: str, response: Any) -> None:
        """Handle service response and extract data for configured sources.

        Args:
            service_name: Name of the service that responded
            response: Service response object
        """
        logger.debug(f"Received service response from {service_name}: {type(response)}")

        # Check if response is valid
        if response is None:
            logger.warning(f"Service {service_name} returned None response")
            # Emit None to all sources that use this service
            for src_name, cfg in self.data_sources.items():
                if cfg.get("service_name") == service_name:
                    self.data_updated.emit(src_name, None)
            return

        # Log response structure for debugging
        if hasattr(response, '__dict__'):
            logger.debug(f"Response attributes: {list(response.__dict__.keys())}")
        elif isinstance(response, dict):
            logger.debug(f"Response keys: {list(response.keys())}")

        # Find all sources that use this service
        for src_name, cfg in self.data_sources.items():
            if cfg.get("service_name") == service_name:
                try:
                    # Check if response has success field and it's False
                    success = getattr(response, 'success', True)  # Default to True if no success field
                    if hasattr(response, 'success') and not success:
                        message = getattr(response, 'message', 'Service call failed')
                        logger.warning(f"Service {service_name} failed: {message}")
                        # Still try to extract data, but log the failure

                    # Extract field from response using existing field extraction logic
                    field_path = cfg.get("field", "")
                    logger.debug(f"Extracting field '{field_path}' from service response")

                    value = self._extract_field_value(response, field_path)
                    logger.debug(f"Extracted value type: {type(value)}, length: {len(value) if isinstance(value, (list, tuple)) else 'N/A'}")

                    # Apply multiplier if configured
                    multiplier = cfg.get("multiplier", 1.0)
                    if multiplier != 1.0:
                        if isinstance(value, (int, float)):
                            value = value * multiplier
                        elif isinstance(value, list) and value:
                            if all(isinstance(v, (int, float)) for v in value):
                                value = [v * multiplier for v in value]

                    # Cache the response
                    self._service_cache[src_name] = value

                    logger.debug(f"Service response for '{src_name}': {type(value)} with {len(value) if isinstance(value, (list, tuple)) else 'N/A'} items")

                    # Emit update
                    self.data_updated.emit(src_name, value)
                    self.last_update_time[src_name] = time.time()
                    if not self.data_is_fresh.get(src_name):
                        self._set_data_freshness(src_name, True)

                except Exception as e:
                    logger.error(f"Error processing service response for {src_name}: {e}")
                    logger.error(f"Response type: {type(response)}")
                    if hasattr(response, '__dict__'):
                        logger.error(f"Response content: {response.__dict__}")
                    # Emit empty data on error
                    self.data_updated.emit(src_name, None)

    # ---------------------------------------------------- publishing helper
    def publish(self, topic: str, msg_type: str, namespace: Optional[str] = None, **kwargs):
        """Publish a message to Zenoh.

        Args:
            topic: Topic name
            msg_type: Message type (e.g. 'drill_msgs/msg/Permission')
            namespace: Namespace to use (defaults to current namespace)
            **kwargs: Additional message fields

        Returns:
            True if message was published, False otherwise
        """
        if not self.zenoh:
            logger.warning("Cannot publish: no Zenoh client")
            return False

        ns = namespace or self.current_namespace
        if not ns:
            logger.warning(f"Cannot publish to topic {topic}: no namespace")
            return False

        try:
            self.zenoh.publish(
                key_expr=topic,
                msg_type=msg_type,
                namespace=ns,
                **kwargs
            )
            return True
        except Exception as e:
            logger.error(f"Error publishing to {topic} in namespace {ns}: {e}")
            return False



    # ---------------------------------------------------- data freshness
    def _set_data_freshness(self, source: str, fresh: bool):
        if self.data_is_fresh.get(source) != fresh:
            logger.debug(f"Source '{source}' in ns '{self.current_namespace}' freshness changed: {fresh}")
            self.data_is_fresh[source] = fresh
            self.data_freshness_changed.emit(source, fresh)

    def _check_data_freshness(self):
        now = time.time()
        for src, t in list(self.last_update_time.items()):
            if now - t > self.data_timeout and self.data_is_fresh.get(src, False):
                logger.debug(f"Source {src} in ns '{self.current_namespace}' is stale: {now - t:.1f}s > {self.data_timeout}s")
                self._set_data_freshness(src, False)

    # ---------------------------------------------------- dispatch helpers
    def _dispatch_update(self, source_name: str, value: Any):
        logger.debug(f"Dispatching update for '{source_name}' in ns '{self.current_namespace}': {value}")

        # Apply any transforms defined in data_sources
        if source_name in self.data_sources and "transform" in self.data_sources[source_name]:
            transform = self.data_sources[source_name]["transform"]
            transform_type = transform.get("type")

            if transform_type == "equals":
                # Check if the value equals a specified value
                value = value == transform.get("value")
            elif transform_type == "not_equals":
                # Check if the value does not equal a specified value
                value = value != transform.get("value")
            elif transform_type == "in":
                # Check if the value is in a list of values
                value = value in transform.get("values", [])
            elif transform_type == "not_in":
                # Check if the value is not in a list of values
                value = value not in transform.get("values", [])

            logger.debug(f"Applied transform '{transform_type}' to '{source_name}', result: {value}")

        # fallback generic handler hook for concrete widgets
        if hasattr(self, "handle_update"):
            self.handle_update(source_name, value)
        spc = getattr(self, f"handle_{source_name}", None)
        if callable(spc):
            spc(value)

    # ---------------------------------------------------- convenience
    def is_data_fresh(self, source: Optional[str] = None) -> bool:
        if source is not None:
            return self.data_is_fresh.get(source, False)
        return bool(self.data_is_fresh) and all(self.data_is_fresh.values())

    def cleanup(self):
        self.freshness_timer.stop()
        self.unsubscribe()
        if hasattr(self.core, "vehicle_changed"):
            try:
                self.core.vehicle_changed.disconnect(self.on_namespace_changed)
            except Exception:
                pass
