#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Тест для проверки извлечения полей из base_widget.py
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

# Создаем минимальную заглушку для PyQt6
class MockQWidget:
    def __init__(self, *args, **kwargs):
        pass

class MockQTimer:
    def __init__(self, *args, **kwargs):
        self.timeout = MockSignal()

    def start(self, *args):
        pass

    def stop(self):
        pass

    def isActive(self):
        return False

class MockSignal:
    def connect(self, *args):
        pass

    def disconnect(self, *args):
        pass

    def emit(self, *args):
        pass

# Подменяем PyQt6 модули
sys.modules['PyQt6'] = type(sys)('PyQt6')
sys.modules['PyQt6.QtCore'] = type(sys)('QtCore')
sys.modules['PyQt6.QtWidgets'] = type(sys)('QtWidgets')

sys.modules['PyQt6.QtCore'].QTimer = MockQTimer
sys.modules['PyQt6.QtCore'].pyqtSignal = lambda *args: MockSignal()
sys.modules['PyQt6.QtWidgets'].QWidget = MockQWidget

# Теперь можем импортировать base_widget
from core.base_widget import ZenohWidget

class MockCore:
    """Заглушка для core объекта"""
    pass

class MockPoint:
    """Заглушка для точки"""
    def __init__(self, x, y):
        self.x = x
        self.y = y

class MockSegment:
    """Заглушка для сегмента"""
    def __init__(self, points):
        self.points = points

class MockMessage:
    """Заглушка для сообщения"""
    def __init__(self, segments):
        self.path_segments = segments

def test_base_widget_extraction():
    """Тест извлечения полей через настоящий ZenohWidget"""
    print("=== Тест извлечения через ZenohWidget ===")

    # Создаем тестовые данные
    segments = [
        MockSegment([
            MockPoint(1.0, 10.0),
            MockPoint(2.0, 20.0)
        ]),
        MockSegment([
            MockPoint(3.0, 30.0),
            MockPoint(4.0, 40.0),
            MockPoint(5.0, 50.0)
        ]),
        MockSegment([
            MockPoint(6.0, 60.0)
        ])
    ]

    message = MockMessage(segments)

    # Создаем виджет
    core = MockCore()
    widget = ZenohWidget(core)

    # Тестируем извлечение 2D массива x-координат
    result_x = widget._extract_field_value(message, "path_segments[].points[].x")
    print(f"2D X-координаты: {result_x}")
    print(f"Тип результата: {type(result_x)}")
    print(f"Количество сегментов: {len(result_x)}")
    for i, segment in enumerate(result_x):
        print(f"  Сегмент {i}: {segment} (длина: {len(segment)})")

    # Проверяем ожидаемый результат
    expected_x = [[1.0, 2.0], [3.0, 4.0, 5.0], [6.0]]
    assert result_x == expected_x, f"Ожидалось {expected_x}, получено {result_x}"
    print("✓ Тест 2D массива x-координат прошел успешно")

    # Тестируем извлечение 1D массива
    points = [MockPoint(1.0, 10.0), MockPoint(2.0, 20.0), MockPoint(3.0, 30.0)]

    class MockPath:
        def __init__(self, points):
            self.points = points

    path_message = MockPath(points)
    result_1d = widget._extract_field_value(path_message, "points[].x")
    print(f"\n1D X-координаты: {result_1d}")
    expected_1d = [1.0, 2.0, 3.0]
    assert result_1d == expected_1d, f"Ожидалось {expected_1d}, получено {result_1d}"
    print("✓ Тест 1D массива прошел успешно")

    # Тестируем простое поле
    class MockRobot:
        def __init__(self, x):
            self.x = x

    robot = MockRobot(42.0)
    result_simple = widget._extract_field_value(robot, "x")
    print(f"\nПростое поле: {result_simple}")
    assert result_simple == 42.0, f"Ожидалось 42.0, получено {result_simple}"
    print("✓ Тест простого поля прошел успешно")

    print("\n🎉 Все тесты ZenohWidget прошли успешно!")

if __name__ == "__main__":
    test_base_widget_extraction()
