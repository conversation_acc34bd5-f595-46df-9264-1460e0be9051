#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Тест для проверки извлечения полей из вложенных структур.
Проверяет, что segments[].points[].x возвращает двумерный массив.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from core.base_widget import ZenohWidget

class MockCore:
    """Заглушка для core объекта"""
    pass

class TestWidget:
    """Тестовый виджет без Qt зависимостей"""
    def __init__(self):
        pass

    def _extract_field_value(self, data, field_path):
        """Копия метода из ZenohWidget для тестирования"""
        if not field_path:
            return data

        parts = field_path.split(".")

        # Count array levels for return type determination
        array_count = sum(1 for part in parts if part.endswith("[]"))

        # Handle multi-dimensional arrays specially
        if array_count >= 2:
            return self._extract_2d_array(data, field_path)

        # Process path step by step
        current = data

        for part in parts:
            if current is None:
                break

            if part.endswith("[]"):
                # Array iteration
                field_name = part[:-2]

                # Get the array field
                if isinstance(current, dict):
                    array_data = current.get(field_name, [])
                else:
                    try:
                        array_data = getattr(current, field_name, [])
                    except AttributeError:
                        array_data = []

                # Ensure it's a list
                if not isinstance(array_data, (list, tuple)):
                    array_data = [array_data] if array_data is not None else []

                current = list(array_data)

            else:
                # Simple field access
                if isinstance(current, (list, tuple)):
                    # Apply to each item in list
                    new_data = []
                    for item in current:
                        if item is None:
                            continue

                        if isinstance(item, dict):
                            val = item.get(part)
                        else:
                            try:
                                val = getattr(item, part)
                            except AttributeError:
                                continue

                        if val is not None:
                            new_data.append(val)

                    current = new_data
                else:
                    # Single item
                    if isinstance(current, dict):
                        current = current.get(part)
                    else:
                        try:
                            current = getattr(current, part)
                        except AttributeError:
                            current = None

        # Return appropriate type based on array count
        if array_count == 0:
            return current
        else:
            return current if isinstance(current, list) else ([] if current is None else [current])

    def _extract_2d_array(self, data, field_path):
        """Extract 2D array preserving structure for paths like segments[].points[].x."""
        parts = field_path.split(".")

        # Find the first array level (e.g., "segments[]")
        first_array_idx = None
        for i, part in enumerate(parts):
            if part.endswith("[]"):
                first_array_idx = i
                break

        if first_array_idx is None:
            return []

        # Extract up to first array level
        current_data = data
        for i in range(first_array_idx):
            part = parts[i]
            if isinstance(current_data, dict):
                current_data = current_data.get(part)
            else:
                try:
                    current_data = getattr(current_data, part)
                except AttributeError:
                    return []

        # Get the first array field (e.g., "segments")
        first_array_field = parts[first_array_idx][:-2]  # Remove []

        if isinstance(current_data, dict):
            segments = current_data.get(first_array_field, [])
        else:
            try:
                segments = getattr(current_data, first_array_field, [])
            except AttributeError:
                segments = []

        if not isinstance(segments, (list, tuple)):
            segments = [segments] if segments is not None else []

        # Build remaining path after first array
        remaining_parts = parts[first_array_idx + 1:]
        remaining_path = ".".join(remaining_parts)

        # Extract data from each segment preserving structure
        result = []
        for segment in segments:
            if segment is None:
                result.append([])
                continue

            # Extract from this segment using remaining path
            segment_data = self._extract_field_value(segment, remaining_path)

            # Ensure segment_data is a list
            if not isinstance(segment_data, list):
                segment_data = [segment_data] if segment_data is not None else []

            result.append(segment_data)

        return result

class MockPoint:
    """Заглушка для точки"""
    def __init__(self, x, y):
        self.x = x
        self.y = y

class MockSegment:
    """Заглушка для сегмента"""
    def __init__(self, points):
        self.points = points

class MockMessage:
    """Заглушка для сообщения"""
    def __init__(self, segments):
        self.path_segments = segments

def test_2d_array_extraction():
    """Тест извлечения двумерного массива"""
    print("=== Тест извлечения двумерного массива ===")

    # Создаем тестовые данные
    segments = [
        MockSegment([
            MockPoint(1.0, 10.0),
            MockPoint(2.0, 20.0)
        ]),
        MockSegment([
            MockPoint(3.0, 30.0),
            MockPoint(4.0, 40.0),
            MockPoint(5.0, 50.0)
        ]),
        MockSegment([
            MockPoint(6.0, 60.0)
        ])
    ]

    message = MockMessage(segments)

    # Создаем виджет для тестирования
    widget = TestWidget()

    # Тестируем извлечение x-координат
    result_x = widget._extract_field_value(message, "path_segments[].points[].x")
    print(f"X-координаты: {result_x}")
    print(f"Тип результата: {type(result_x)}")
    print(f"Количество сегментов: {len(result_x)}")
    for i, segment in enumerate(result_x):
        print(f"  Сегмент {i}: {segment} (длина: {len(segment)})")

    # Проверяем ожидаемый результат
    expected_x = [[1.0, 2.0], [3.0, 4.0, 5.0], [6.0]]
    assert result_x == expected_x, f"Ожидалось {expected_x}, получено {result_x}"
    print("✓ Тест x-координат прошел успешно")

    # Тестируем извлечение y-координат
    result_y = widget._extract_field_value(message, "path_segments[].points[].y")
    print(f"\nY-координаты: {result_y}")
    expected_y = [[10.0, 20.0], [30.0, 40.0, 50.0], [60.0]]
    assert result_y == expected_y, f"Ожидалось {expected_y}, получено {result_y}"
    print("✓ Тест y-координат прошел успешно")

    print("\n=== Все тесты прошли успешно! ===")

def test_1d_array_extraction():
    """Тест извлечения одномерного массива"""
    print("\n=== Тест извлечения одномерного массива ===")

    # Создаем тестовые данные для одномерного массива
    points = [
        MockPoint(1.0, 10.0),
        MockPoint(2.0, 20.0),
        MockPoint(3.0, 30.0)
    ]

    class MockPath:
        def __init__(self, points):
            self.points = points

    message = MockPath(points)

    # Создаем виджет для тестирования
    widget = TestWidget()

    # Тестируем извлечение x-координат (1D)
    result_x = widget._extract_field_value(message, "points[].x")
    print(f"X-координаты (1D): {result_x}")
    print(f"Тип результата: {type(result_x)}")

    expected_x = [1.0, 2.0, 3.0]
    assert result_x == expected_x, f"Ожидалось {expected_x}, получено {result_x}"
    print("✓ Тест 1D массива прошел успешно")

def test_simple_field_extraction():
    """Тест извлечения простого поля"""
    print("\n=== Тест извлечения простого поля ===")

    class MockRobot:
        def __init__(self, x):
            self.x = x

    message = MockRobot(42.0)

    # Создаем виджет для тестирования
    widget = TestWidget()

    # Тестируем извлечение простого поля
    result = widget._extract_field_value(message, "x")
    print(f"Простое поле x: {result}")
    print(f"Тип результата: {type(result)}")

    assert result == 42.0, f"Ожидалось 42.0, получено {result}"
    print("✓ Тест простого поля прошел успешно")

if __name__ == "__main__":
    test_simple_field_extraction()
    test_1d_array_extraction()
    test_2d_array_extraction()
    print("\n🎉 Все тесты завершены успешно!")
